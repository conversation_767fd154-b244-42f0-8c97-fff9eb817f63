# Connections
The connections module supports connecting to various structured, semi-structured, and unstructured data storage engines. Bring multi-dimensional data into the framework and realize the interaction between natural language and multi-dimensional data

The list of data sources we currently support is as follows.

| DataSource                                                                      | support | Notes                                       |
| ------------------------------------------------------------------------------  |---------| ------------------------------------------- |
| [MySQL](https://www.mysql.com/)                                                 | Yes     |  MySQL is the world's most popular open source database.                             |
| [PostgresSQL](https://www.postgresql.org/)                                      | Yes     |  The World's Most Advanced Open Source Relational Database                                   |
| [Vertica](https://www.vertica.com/)                                             | Yes     |  Vertica is a strongly consistent, ACID-compliant, SQL data warehouse, built for the scale and complexity of today’s data-driven world.                                   |
| [Spark](https://github.com/apache/spark)                                        | Yes     |  Unified Engine for large-scale data analytics                                |
| [DuckDB](https://github.com/duckdb/duckdb)                                      | Yes     |  DuckDB is an in-process SQL OLAP database management system                                          |
| [Sqlite](https://github.com/sqlite/sqlite)                                      | Yes     |                                             |
| [MSSQL](https://github.com/microsoft/mssql-jdbc)                                | Yes     |                                             |
| [ClickHouse](https://github.com/ClickHouse/ClickHouse)                          | Yes     |  ClickHouse is the fastest and most resource efficient open-source database for real-time apps and analytics.                                      |
| [Oracle](https://github.com/oracle)                                             | No      |           TODO                              |
| [Redis](https://github.com/redis/redis)                                         | No      |  The Multi-model NoSQL Database                              |
| [MongoDB](https://github.com/mongodb/mongo)                                     | No      |  MongoDB is a source-available cross-platform document-oriented database program                              |
| [HBase](https://github.com/apache/hbase)                                        | No      |  Open-source, distributed, versioned, column-oriented store modeled                              |
| [Doris](https://github.com/apache/doris)                                        | Yes     |  Apache Doris is an easy-to-use, high performance and unified analytics database.                              |
| [DB2](https://github.com/IBM/Db2)                                               | No      |           TODO                              |
| [Couchbase](https://github.com/couchbase)                                       | No      |           TODO                              |
| [Elasticsearch](https://github.com/elastic/elasticsearch)                       | No      |  Free and Open, Distributed, RESTful Search Engine                              |
| [OceanBase](https://github.com/OceanBase)                                       | No      |  OceanBase is a distributed relational database.                               |
| [TiDB](https://github.com/pingcap/tidb)                                         | No      |           TODO                              |
| [StarRocks](https://github.com/StarRocks/starrocks)                             | Yes     | StarRocks is a next-gen, high-performance analytical data warehouse                               |
