FROM eosphorosai/dbgpt-full:latest
ARG PYTHON_VERSION=3.11
ARG PIP_INDEX_URL="https://pypi.tuna.tsinghua.edu.cn/simple"
ARG USERNAME
ARG EXTRAS="base,proxy_openai,graph_rag,rag,storage_chromadb, storage_elasticsearch,cuda121,hf,quant_bnb,dbgpts"
ARG DEFAULT_VENV=/opt/.uv.venv
WORKDIR /app
COPY . .
USER root
# Set the GID and UID of the container and 
# add a user to prevent permission mismatches
# between the container user (root) and the host user,
# and to resolve the issue of the host user lacking write permissions.
RUN . .devcontainer/.env && \
    groupadd -g $USER_GID $GROUPNAME && \
    useradd -u $USER_UID -g $USER_GID -m $USERNAME && \
    chown -R $USER_UID:$USER_GID /app
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    python${PYTHON_VERSION}-dev \
    default-libmysqlclient-dev \
    ssh zsh autojump curl git-flow vim sudo \
    fonts-wqy-microhei fonts-noto-cjk \
    locales \
    && sed -i '/zh_CN.UTF-8/s/^# //g' /etc/locale.gen \
    && locale-gen zh_CN.UTF-8 \
    && update-locale LANG=zh_CN.UTF-8 \
    && python${PYTHON_VERSION} -m pip install --upgrade pip \
    && python${PYTHON_VERSION} -m pip install --upgrade pipx \
    && pipx install -i $PIP_INDEX_URL uv --global \
    && chown -R $USERNAME:$GROUPNAME $DEFAULT_VENV \
    && echo "$USERNAME ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/$USERNAME \
    && chmod 0440 /etc/sudoers.d/$USERNAME
USER $USERNAME
ENV UV_LINK_MODE=copy \
    PIP_INDEX_URL=$PIP_INDEX_URL \
    VIRTUAL_ENV=$DEFAULT_VENV \
    UV_PROJECT_ENVIRONMENT=$DEFAULT_VENV \
    UV_PYTHON=$DEFAULT_VENV/bin/python3 \
    UV_INDEX=$PIP_INDEX_URL \
    UV_DEFAULT_INDEX=$PIP_INDEX_URL \
    LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8

RUN sed -i "s|/app/\.venv|${FINAL_VENV_NAME}|g" /${DEFAULT_VENV}/bin/activate && \
    pip config set global.index-url $PIP_INDEX_URL && \
    pip config set global.trusted-host $(echo "$PIP_INDEX_URL" | sed -E 's|^https?://([^/]+).*|\1|') && \
    . $DEFAULT_VENV/bin/activate && \
    extras=$(echo $EXTRAS | tr ',' '\n' | while read extra; do echo "--extra $extra"; done | tr '\n' ' ') && \
    uv sync -v --active --all-packages $extras --default-index $PIP_INDEX_URL  && \
    uv pip -v install --prefix $VIRTUAL_ENV -r requirements/dev-requirements.txt && \
    uv pip -v install --prefix $VIRTUAL_ENV -r requirements/lint-requirements.txt && \
    cp .devcontainer/dbgpt.pth  /opt/.uv.venv/lib/python${PYTHON_VERSION}/site-packages/dbgpt.pth && \
    python -c "import dbgpt; print(dbgpt.__version__)"