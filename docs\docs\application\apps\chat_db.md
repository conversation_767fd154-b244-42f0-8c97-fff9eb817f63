# Chat DB

The purpose of `Chat DB` is to create professional database experts, positioned as LLM As DBA, who can complete database performance analysis, optimization and other work by talking to the database. Currently, ChatDB only has some basic capabilities, which will be gradually enhanced with the iteration of the community.


## Steps

The Chat DB usage process mainly includes the following steps:
- 1.Select Chat DB
- 2.Add data source (talk to data)
- 3.Select the basic model and database
- 4.Start chat

### Select Chat DB

<p align="left">
  <img src={'/img/chat_db/choose_chat_db.png'} width="720px" />
</p>



### Select DataBase

<p align="left">
  <img src={'/img/chat_db/choose_db.png'} width="720px" />
</p>


### Start Chat

:::tip

Single table query
:::

<p align="left">
  <img src={'/img/chat_db/single_table.png'} width="720px" />
</p>

:::tip

Multi-table query
:::

<p align="left">
  <img src={'/img/chat_db/multi_table.png'} width="720px" />
</p>

:::tip

Index optimization suggestions
:::

<p align="left">
  <img src={'/img/chat_db/index.png'} width="720px" />
</p>


:::tip

Database problem diagnosis
:::

<p align="left">
  <img src={'/img/chat_db/problem_help.png'} width="720px" />
</p>


:::tip

Troubleshoot slow queries
:::

<p align="left">
  <img src={'/img/chat_db/slow_query.png'} width="720px" />
</p>


:::danger Note

⚠️ The examples provided above are for demonstration only. The model output results are from open source models and ChatGPT agents. They have not been fine-tuned or targeted optimized. They are for reference only and are not guaranteed to be absolutely correct.
:::
