# Chinese translations for PACKAGE package
# PACKAGE 软件包的简体中文翻译.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 00:06+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:35
#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:145
msgid "Context Key"
msgstr "上下文键"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:40
#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:150
msgid "The key of the context, it will be used in building the prompt"
msgstr "上下文的键，它将用于构建提示信息"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:43
msgid "Top K"
msgstr "前 K 项"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:48
msgid "The number of chunks to retrieve"
msgstr "要检索的块数量"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:51
msgid "Minimum Match Score"
msgstr "最小匹配分数"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:58
msgid ""
"The minimum match score for the retrieved chunks, it will be dropped if the "
"match score is less than the threshold"
msgstr "检索到的块的最小匹配分数，如果匹配分数低于阈值，则会被丢弃"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:66
msgid "Reranker Enabled"
msgstr "是否启用重排序器"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:71
msgid "Whether to enable the reranker"
msgstr "是否启用重排序器"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:74
msgid "Reranker Top K"
msgstr "重排序器的前 K 项"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:79
msgid "The top k for the reranker"
msgstr "重排序器的前 K 项"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:83
#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:153
msgid "User question"
msgstr "用户问题"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:86
msgid "The user question to retrieve the knowledge"
msgstr "用于检索知识的用户问题"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:89
#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:159
msgid "Retrieved context"
msgstr "检索到的上下文"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:92
msgid "The retrieved context from the knowledge space"
msgstr "从知识空间检索到的上下文"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:107
msgid "Knowledge Space Operator"
msgstr "知识空间算子"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:111
msgid "Knowledge Space Operator, retrieve your knowledge from knowledge space"
msgstr "知识空间算子，从知识空间检索知识"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:115
msgid "Knowledge Space Name"
msgstr "知识空间名称"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:119
msgid "The name of the knowledge space"
msgstr "知识空间的名称"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:133
msgid "Chunks"
msgstr "片段"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/rag.py:137
msgid "The retrieved chunks from the knowledge space"
msgstr "从知识空间检索到的片段"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:15
#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:40
msgid "String"
msgstr "字符串"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:18
msgid "The string to be converted to other types."
msgstr "要转换为其他类型的字符串"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:21
#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:46
msgid "Integer"
msgstr "整数"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:24
msgid "The integer to be converted to other types."
msgstr "要转换为其他类型的整数"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:27
#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:52
msgid "Float"
msgstr "浮点数"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:30
msgid "The float to be converted to other types."
msgstr "要转换为其他类型的浮点数"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:33
#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:58
msgid "Boolean"
msgstr "布尔值"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:36
msgid "The boolean to be converted to other types."
msgstr "要转换为其他类型的布尔值"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:43
msgid "The string converted from other types."
msgstr "从其他类型转换而来的字符串"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:49
msgid "The integer converted from other types."
msgstr "从其他类型转换而来的整数"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:55
msgid "The float converted from other types."
msgstr "从其他类型转换而来的浮点数"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:61
msgid "The boolean converted from other types."
msgstr "从其他类型转换而来的布尔值"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:69
msgid "String to Integer"
msgstr "字符串转整数"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:71
msgid "Converts a string to an integer."
msgstr "将字符串转换为整数。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:88
msgid "String to Float"
msgstr "字符串转浮点数"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:90
msgid "Converts a string to a float."
msgstr "将字符串转换为浮点数。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:107
msgid "String to Boolean"
msgstr "字符串转布尔值"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:109
msgid "Converts a string to a boolean, true: 'true', '1', 'y'"
msgstr "将字符串转换为布尔值，真值：'true'、'1'、'y'"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:113
msgid "True Values"
msgstr "真值"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:118
msgid "Comma-separated values that should be treated as True."
msgstr "应被视为真的逗号分隔值。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:137
msgid "Integer to String"
msgstr "整数转字符串"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:139
msgid "Converts an integer to a string."
msgstr "将整数转换为字符串。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:156
msgid "Float to String"
msgstr "浮点数转字符串"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:158
msgid "Converts a float to a string."
msgstr "将浮点数转换为字符串。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:175
msgid "Boolean to String"
msgstr "布尔值转字符串"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:177
msgid "Converts a boolean to a string."
msgstr "将布尔值转换为字符串。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:194
msgid "Model Output to Dict"
msgstr "模型输出转字典"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:196
msgid "Converts a model output to a dictionary."
msgstr "将模型输出转换为字典。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:199
#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:391
msgid "Model Output"
msgstr "模型输出"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/converter.py:200
msgid "Dictionary"
msgstr "字典"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:103
msgid "Datasource"
msgstr "数据源"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:106
msgid "The datasource to retrieve the context"
msgstr "用于获取上下文的数据源"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:109
#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:283
msgid "Prompt Template"
msgstr "提示模板"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:114
msgid "The prompt template to build a database prompt"
msgstr "用于构建数据库提示的提示模板"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:118
msgid "Display Type"
msgstr "显示类型"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:123
msgid "The display type for the data"
msgstr "数据的显示类型"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:127
msgid "Max Number of Results"
msgstr "最大结果数量"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:132
msgid "The maximum number of results to return"
msgstr "返回的最大结果数量"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:135
msgid "Response Format"
msgstr "响应格式"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:140
msgid "The response format, default is a JSON format"
msgstr "响应格式，默认为 JSON 格式"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:156
msgid "The user question to retrieve table schemas from the datasource"
msgstr "用户用于从数据源检索表结构的问题"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:162
msgid "The retrieved context from the datasource"
msgstr "从数据源检索到的上下文"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:166
msgid "SQL dict"
msgstr "SQL 字典"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:169
msgid "The SQL to be executed wrapped in a dictionary, generated by LLM"
msgstr "由大语言模型生成并封装在字典中的待执行 SQL"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:172
msgid "SQL result"
msgstr "SQL 结果"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:175
msgid "The result of the SQL execution"
msgstr "SQL 执行结果"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:179
msgid "SQL dict list"
msgstr "SQL 字典列表"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:183
msgid "The SQL list to be executed wrapped in a dictionary, generated by LLM"
msgstr "由大语言模型生成并封装在字典中的待执行 SQL 列表"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:212
msgid "Datasource Retriever Operator"
msgstr "数据源检索算子"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:214
msgid "Retrieve the table schemas from the datasource."
msgstr "从数据源检索表结构。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:228
msgid "Retrieved schema chunks"
msgstr "已检索的结构片段"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:232
msgid "The retrieved schema chunks from the datasource"
msgstr "从数据源检索到的结构片段"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:290
msgid "Datasource Executor Operator"
msgstr "数据源执行算子"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:292
#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:329
msgid "Execute the context from the datasource."
msgstr "从数据源执行上下文。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/datasource.py:327
msgid "Datasource Dashboard Operator"
msgstr "数据源仪表板算子"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:60
msgid "Code Map Operator"
msgstr "代码映射算子"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:63
msgid ""
"Handle input dictionary with code and return output dictionary after "
"execution."
msgstr "处理包含代码的输入字典，并在执行后返回输出字典。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:69
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:211
msgid "Code Editor"
msgstr "代码编辑器"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:74
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:216
msgid "Please input your code"
msgstr "请输入您的代码"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:75
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:217
msgid "The code to be executed."
msgstr "要执行的代码。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:81
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:223
msgid "Language"
msgstr "语言"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:86
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:228
msgid "Please select the language"
msgstr "请选择语言"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:87
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:229
msgid "The language of the code."
msgstr "代码的语言。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:97
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:239
msgid "Call Name"
msgstr "调用名称"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:102
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:244
msgid "Please input the call name"
msgstr "请输入调用名称"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:103
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:245
msgid "The call name of the function."
msgstr "函数的调用名称。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:108
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:250
msgid "Input Data"
msgstr "输入数据"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:111
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:253
msgid "The input dictionary."
msgstr "输入字典。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:116
#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:258
msgid "Output Data"
msgstr "输出数据"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:119
msgid "The output dictionary."
msgstr "输出字典。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:202
msgid "Code Dict to Model Request Operator"
msgstr "代码字典到模型请求算子"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:205
msgid ""
"Handle input dictionary with code and return output ModelRequest after "
"execution."
msgstr "处理包含代码的输入字典，并在执行后返回输出的 ModelRequest。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/code.py:261
msgid "The output ModelRequest."
msgstr "输出的 ModelRequest。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:53
msgid "The context key can be used as the key for formatting prompt."
msgstr "上下文键可用作格式化提示的键。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:57
msgid "The context."
msgstr "上下文。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:63
msgid "You are a helpful AI assistant."
msgstr "你是一个乐于助人的 AI 助手。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:286
msgid "The prompt template for the conversation."
msgstr "对话的提示模板。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:291
msgid "Model Name"
msgstr "模型名称"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:296
msgid "The model name."
msgstr "模型名称。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:300
msgid "LLM Client"
msgstr "LLM 客户端"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:306
msgid ""
"The LLM Client, how to connect to the LLM model, if not provided, it will "
"use the default client deployed by DB-GPT."
msgstr "大语言模型客户端，用于连接到大语言模型，若未提供，则使用 DB-GPT 部署的默认客户端。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:311
msgid "History Message Merge Mode"
msgstr "历史消息合并模式"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:322
msgid ""
"The history merge mode, supports 'none', 'window' and 'token'. 'none': no "
"history merge, 'window': merge by conversation window, 'token': merge by "
"token length."
msgstr "历史合并模式，支持 'none'、'window' 和 'token'。'none'：不合并历史，'window'：按对话窗口合并，'token'：按 Token 长度合并。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:329
msgid "User Message Key"
msgstr "用户消息键"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:335
msgid "The key of the user message in your prompt, default is 'user_input'."
msgstr "提示词中用户消息的键，默认为 'user_input'。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:339
msgid "History Key"
msgstr "历史键"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:345
msgid ""
"The chat history key, with chat history message pass to prompt template, if "
"not provided, it will parse the prompt template to get the key."
msgstr "聊天历史键，用于将聊天历史消息传递给提示模板，若未提供，将解析提示模板以获取该键。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:350
msgid "Keep Start Rounds"
msgstr "保留起始轮次"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:355
msgid "The start rounds to keep in the chat history."
msgstr "在聊天历史中保留的起始轮次。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:358
msgid "Keep End Rounds"
msgstr "保留结束轮次"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:363
msgid "The end rounds to keep in the chat history."
msgstr "在聊天历史中保留的结束轮次。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:366
msgid "Max Token Limit"
msgstr "最大 Token 限制"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:371
msgid "The max token limit to keep in the chat history."
msgstr "在聊天历史中保留的最大 Token 数量。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:375
msgid "Common LLM Request Body"
msgstr "通用大语言模型请求体"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:378
msgid "The common LLM request body."
msgstr "通用大语言模型请求体。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:381
msgid "Extra Context"
msgstr "额外上下文"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:385
msgid ""
"Extra context for building prompt(Knowledge context, database schema, etc), "
"you can add multiple context."
msgstr "用于构建提示的额外上下文（知识上下文、数据库模式等），您可以添加多个上下文。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:394
msgid "The model output."
msgstr "模型输出。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:397
msgid "Streaming Model Output"
msgstr "流式模型输出"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:401
msgid "The streaming model output."
msgstr "流式模型输出。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:407
msgid "LLM Operator"
msgstr "大语言模型算子"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:411
msgid ""
"High-level LLM operator, supports multi-round conversation (conversation "
"window, token length and no multi-round)."
msgstr "高级大语言模型算子，支持多轮对话（对话窗口、Token 长度和无多轮）。"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:441
msgid "Streaming LLM Operator"
msgstr "流式大语言模型算子"

#: ../packages/dbgpt-app/src/dbgpt_app/operators/llm.py:445
msgid ""
"High-level streaming LLM operator, supports multi-round conversation "
"(conversation window, token length and no multi-round)."
msgstr "高级流式大语言模型算子，支持多轮对话（对话窗口、Token 长度和无多轮）。"