# App Chat

The online Chat interface provides the main conversation capabilities, showing the historical conversation records and the application currently in conversation. As shown in the figure below, clicking any smart application will also jump to this interface.

<p align="center">
  <img src={'/img/app/app_chat_v0.6.jpg'} width="800px" />
</p>

In the dialogue interface, a series of operations such as refreshing and pausing the dialogue are supported. The specific operation buttons are in the edit box at the bottom right. At the same time, the dialog box also provides a variety of parameter selections, such as model selection, temperature parameter adjustment, file upload, etc.

<p align="center">
  <img src={'/img/app/app_chat_op_v0.6.jpg'} width="800" />
</p>

If you find new problems or have good ideas during use, you can also directly post them on Github [issue](https://github.com/eosphoros-ai/DB-GPT/issues) feedback.
