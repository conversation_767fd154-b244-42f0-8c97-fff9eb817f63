version: '3.8'

services:
  oceanbase:
    image: oceanbase/oceanbase-ce:vector
    ports: 
      - 2881:2881
    networks:
      - dbgptnet
  dbgpt:
    image: eosphorosai/dbgpt-allinone
    environment:
      - DBGPT_WEBSERVER_PORT=12345
      - VECTOR_STORE_TYPE=OceanBase
      - OB_HOST=oceanbase
      - OB_HOST=127.0.0.1
      - OB_PORT=2881
      - OB_USER=root@test
      - OB_DATABASE=test
      - LOCAL_DB_TYPE=sqlite
      - LLM_MODEL=tongyi_proxyllm
      - PROXYLLM_BACKEND=qwen-plus
      - EMBEDDING_MODEL=text2vec
      # your api key
      # - TONGYI_PROXY_API_KEY={your-api-key}
      - LANGUAGE=zh
      # - OB_ENABLE_NORMALIZE_VECTOR=True
    ports:
      - 3306:3306
      - 12345:12345
    volumes:
      # - {your-ob-sql-dbg-log-dir}:/sql_log
      # - {your-model-dir}:/app/models
    networks:
      - dbgptnet

networks:
  dbgptnet:
    driver: bridge
    name: dbgptnet