# 3.3 Handling Post Requests

The `HttpTrigger` operator can also handle POST requests. In this section, we will 
create a new HTTP trigger that returns a json response based on the request body of the POST request.

## Say Hello To Someone

Create a new file named `http_trigger_say_hello_post.py` in the `awel_tutorial` directory and add the following code:

```python
from dbgpt._private.pydantic import BaseModel, Field
from dbgpt.core.awel import DAG, HttpTrigger, MapOperator, setup_dev_environment

class TriggerReqBody(BaseModel):
    name: str = Field(..., description="User name")
    age: int = Field(18, description="User age")

with DAG("awel_say_hello_post") as dag:
    trigger_task = HttpTrigger(
        endpoint="/awel_tutorial/say_hello_post", 
        methods="POST", 
        request_body=TriggerReqBody,
        status_code=200
    )
    task = MapOperator(
        map_function=lambda x: {"message": f"Hello, {x.name}! You are {x.age} years old."}
    )
    trigger_task >> task

setup_dev_environment([dag], port=5555)
```

And run the following command to execute the code:

```bash
poetry run python awel_tutorial/http_trigger_say_hello_post.py
```

Now, open a new terminal and run the following command to send a POST request to the server:

```bash
curl -X POST \
"http://127.0.0.1:5555/api/v1/awel/trigger/awel_tutorial/say_hello_post" \
-H "Content-Type: application/json" \
-d '{"name": "John", "age": 25}'
```

The output should look like this:

```plaintext
{"message":"Hello, John! You are 20 years old."}
```

Then you can stop the server by pressing `Ctrl+C`.