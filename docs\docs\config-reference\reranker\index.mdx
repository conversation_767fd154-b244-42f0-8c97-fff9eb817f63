---
title: "reranker"
description: "reranker Configuration"
---

# reranker Configuration

This document provides an overview of all configuration classes in reranker type.

import { ConfigClassTable } from '@site/src/components/mdx/ConfigClassTable';

## Configuration Classes

<ConfigClassTable classes={[
  {
    "name": "CrossEncoderRerankEmbeddingsParameters",
    "description": "CrossEncoder Rerank Embeddings Parameters.",
    "link": "./rerank_crossencoderrerankembeddingsparameters_63ec13"
  },
  {
    "name": "OpenAPIRerankerDeployModelParameters",
    "description": "OpenAPI Reranker Deploy Model Parameters.",
    "link": "./rerank_openapirerankerdeploymodelparameters_778108"
  },
  {
    "name": "SiliconFlowRerankEmbeddingsParameters",
    "description": "SiliconFlow Rerank Embeddings Parameters.",
    "link": "./rerank_siliconflowrerankembeddingsparameters_af0257"
  },
]} />

