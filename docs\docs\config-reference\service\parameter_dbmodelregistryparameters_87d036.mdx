---
title: "DBModelRegistryParameters Configuration"
description: "Database model registry parameters."
---

import { ConfigDetail } from "@site/src/components/mdx/ConfigDetail";

<ConfigDetail config={{
  "name": "DBModelRegistryParameters",
  "description": "Database model registry parameters.",
  "documentationUrl": "",
  "parameters": [
    {
      "name": "database",
      "type": "BaseDatasourceParameters",
      "required": false,
      "description": "Database configuration for model registry",
      "nestedTypes": [
        {
          "type": "link",
          "text": "rdbmsdatasourceparameters configuration",
          "url": "../datasource/base_rdbmsdatasourceparameters_4f774f"
        },
        {
          "type": "link",
          "text": "sqlite configuration",
          "url": "../datasource/conn_sqlite_sqliteconnectorparameters_82c8b5"
        },
        {
          "type": "link",
          "text": "tugraph configuration",
          "url": "../datasource/conn_tugraph_tugraphparameters_0c844e"
        },
        {
          "type": "link",
          "text": "spark configuration",
          "url": "../datasource/conn_spark_sparkparameters_174bbc"
        },
        {
          "type": "link",
          "text": "clickhouse configuration",
          "url": "../datasource/conn_clickhouse_clickhouseparameters_4a1237"
        },
        {
          "type": "link",
          "text": "doris configuration",
          "url": "../datasource/conn_doris_dorisparameters_e33c53"
        },
        {
          "type": "link",
          "text": "duckdb configuration",
          "url": "../datasource/conn_duckdb_duckdbconnectorparameters_c672c7"
        },
        {
          "type": "link",
          "text": "hive configuration",
          "url": "../datasource/conn_hive_hiveparameters_ec3601"
        },
        {
          "type": "link",
          "text": "mssql configuration",
          "url": "../datasource/conn_mssql_mssqlparameters_d79d1c"
        },
        {
          "type": "link",
          "text": "mysql configuration",
          "url": "../datasource/conn_mysql_mysqlparameters_4393c4"
        },
        {
          "type": "link",
          "text": "oceanbase configuration",
          "url": "../datasource/conn_oceanbase_oceanbaseparameters_260d2d"
        },
        {
          "type": "link",
          "text": "postgresql configuration",
          "url": "../datasource/conn_postgresql_postgresqlparameters_22efa5"
        },
        {
          "type": "link",
          "text": "starrocks configuration",
          "url": "../datasource/conn_starrocks_starrocksparameters_e511f7"
        },
        {
          "type": "link",
          "text": "vertica configuration",
          "url": "../datasource/conn_vertica_verticaparameters_c712b8"
        }
      ]
    }
  ]
}} />

