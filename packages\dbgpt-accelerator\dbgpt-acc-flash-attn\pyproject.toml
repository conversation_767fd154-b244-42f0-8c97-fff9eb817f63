# Install the flash-attn package for uv
# https://github.com/astral-sh/uv/issues/2252#issuecomment-2624150395
[project]
name = "dbgpt-acc-flash-attn"
version = "0.7.1"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = []


[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/dbgpt_acc_flash_attn"]

[dependency-groups]
build = [
    "setuptools>=75.8.0",
]
direct = [
    "torch>=2.2.1",
]
main = [
    "flash-attn>=2.5.8",
]

[tool.uv]
default-groups = ["build", "direct", "main"]
no-build-isolation-package = ["flash-attn"]
