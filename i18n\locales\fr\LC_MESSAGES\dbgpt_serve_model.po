# French translations for PACKAGE package.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 00:06+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: ../packages/dbgpt-serve/src/dbgpt_serve/model/config.py:22
msgid "Model Serve Configurations"
msgstr "Configurations du service de modèle"

#: ../packages/dbgpt-serve/src/dbgpt_serve/model/config.py:25
msgid "This configuration is for the model serve module."
msgstr "Cette configuration est destinée au module de service de modèle."

#: ../packages/dbgpt-serve/src/dbgpt_serve/model/config.py:38
msgid ""
"The storage type of model configures, if None, use the default "
"storage(current database). When you run in light mode, it will not use any "
"storage."
msgstr ""
"Le type de stockage des configurations de modèle. Si aucun n'est spécifié, "
"utilisez le stockage par défaut (base de données actuelle). Lorsque vous "
"exécutez en mode léger, il n'utilisera aucun stockage."