# Chinese translations for PACKAGE package
# PACKAGE 软件包的简体中文翻译.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 07:51+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:135
msgid "Dict Http Body"
msgstr "字典型 HTTP 请求体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:139
msgid "Parse the request body as a dict or response body as a dict"
msgstr "将请求体或响应体解析为字典"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:165
msgid "String Http Body"
msgstr "字符串型 HTTP 请求体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:169
msgid "Parse the request body as a string or response body as string"
msgstr "将请求体或响应体解析为字符串"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:195
msgid "Request Http Body"
msgstr "HTTP 请求体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:199
msgid "Parse the request body as a starlette Request"
msgstr "将请求体解析为 Starlette 请求对象"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:227
#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:110
#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:225
msgid "Common LLM Http Request Body"
msgstr "通用大语言模型 HTTP 请求体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:231
msgid "Parse the request body as a common LLM http body"
msgstr "将请求体解析为通用大语言模型 HTTP 请求体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:307
msgid "Common LLM Http Response Body"
msgstr "通用大语言模型 HTTP 响应体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:311
msgid "Parse the response body as a common LLM http body"
msgstr "将响应体解析为通用大语言模型 HTTP 响应体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:759
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:991
msgid "API Endpoint"
msgstr "API 端点"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:759
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:996
msgid "The API endpoint"
msgstr "该 API 端点"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:762
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:774
msgid "Http Methods"
msgstr "HTTP 方法"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:767
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:779
msgid "The methods of the API endpoint"
msgstr "API 端点的方法"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:769
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:783
msgid "HTTP Method PUT"
msgstr "HTTP PUT 方法"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:770
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:784
msgid "HTTP Method POST"
msgstr "HTTP POST 方法"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:781
msgid "HTTP Method GET"
msgstr "HTTP GET 方法"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:782
msgid "HTTP Method DELETE"
msgstr "HTTP DELETE 方法"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:788
msgid "Streaming Response"
msgstr "流式响应"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:793
msgid "Whether the response is streaming"
msgstr "响应是否为流式"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:796
msgid "Http Response Body"
msgstr "HTTP 响应体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:801
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1079
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1130
msgid "The response body of the API endpoint"
msgstr "API 端点的响应体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:805
msgid "Response Media Type"
msgstr "响应媒体类型"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:810
msgid "The response media type"
msgstr "响应媒体类型"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:813
msgid "Http Status Code"
msgstr "HTTP 状态码"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:818
msgid "The http status code"
msgstr "HTTP 状态码"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:829
msgid "Dict Http Trigger"
msgstr "字典 HTTP 触发器"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:834
msgid ""
"Trigger your workflow by http request, and parse the request body as a dict"
msgstr "通过 HTTP 请求触发工作流，并将请求体解析为字典"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:840
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:899
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:970
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1119
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1176
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1225
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:42
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:98
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:97
msgid "Request Body"
msgstr "请求体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:843
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1122
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1179
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1228
msgid "The request body of the API endpoint"
msgstr "API 端点的请求体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:888
msgid "String Http Trigger"
msgstr "字符串 HTTP 触发器"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:893
msgid ""
"Trigger your workflow by http request, and parse the request body as a string"
msgstr "通过 HTTP 请求触发工作流，并将请求体解析为字符串"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:903
msgid "The request body of the API endpoint, parse as a json string"
msgstr "API 端点的请求体，解析为 JSON 字符串"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:959
msgid "Common LLM Http Trigger"
msgstr "通用大语言模型 HTTP 触发器"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:964
msgid ""
"Trigger your workflow by http request, and parse the request body as a "
"common LLM http body"
msgstr "通过 HTTP 请求触发工作流，并将请求体解析为通用大语言模型 HTTP 请求体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:974
msgid "The request body of the API endpoint, parse as a common LLM http body"
msgstr "API 端点的请求体，解析为通用大语言模型 HTTP 请求体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:979
msgid "Request String Messages"
msgstr "请求字符串消息"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:983
msgid ""
"The request string messages of the API endpoint, parsed from 'messages' "
"field of the request body"
msgstr "API 端点的请求字符串消息，从请求体的 'messages' 字段解析而来"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1036
msgid "Example Http Response"
msgstr "示例 HTTP 响应"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1040
msgid "Example Http Request"
msgstr "示例 HTTP 请求"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1062
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1082
msgid "Example Http Hello Operator"
msgstr "示例 HTTP 问候算子"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1068
msgid "Http Request Body"
msgstr "HTTP 请求体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1071
msgid "The request body of the API endpoint(Dict[str, Any])"
msgstr "API 端点的请求体 (Dict[str, Any])"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1076
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1127
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:106
msgid "Response Body"
msgstr "响应体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1102
msgid "Request Body To Dict Operator"
msgstr "请求体转字典算子"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1107
msgid "Prefix Key"
msgstr "前缀键"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1113
msgid "The prefix key of the dict, link 'message' or 'extra.info'"
msgstr "字典的前缀键，例如 'message' 或 'extra.info'"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1161
msgid "User Input Parsed Operator"
msgstr "用户输入解析算子"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1166
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1215
msgid "Key"
msgstr "键"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1171
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1220
msgid "The key of the dict, link 'user_input'"
msgstr "字典的键，例如 'user_input'"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1184
msgid "User Input Dict"
msgstr "用户输入字典"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1187
#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1236
msgid "The user input dict of the API endpoint"
msgstr "API 端点的用户输入字典"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1191
msgid ""
"User input parsed operator, parse the user input from request body and "
"return as a dict"
msgstr "用户输入解析算子，从请求体中解析用户输入并以字典形式返回"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1210
msgid "Request Body Parsed To String Operator"
msgstr "请求体解析为字符串算子"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1233
msgid "User Input String"
msgstr "用户输入字符串"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/http_trigger.py:1240
msgid ""
"User input parsed operator, parse the user input from request body and "
"return as a string"
msgstr "用户输入解析算子，从请求体中解析用户输入并以字符串形式返回"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:31
msgid "Request Http Trigger"
msgstr "HTTP 请求触发器"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:36
msgid ""
"Trigger your workflow by http request, and parse the request body as a "
"starlette Request"
msgstr "通过 HTTP 请求触发工作流，并将请求体解析为 Starlette 请求"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:46
msgid "The request body of the API endpoint, parse as a starlette Request"
msgstr "API 端点的请求体，解析为 Starlette 请求"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:92
msgid "HTTP Sender"
msgstr "HTTP 发送器"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:95
msgid "Send a HTTP request to a specified endpoint"
msgstr "向指定端点发送 HTTP 请求"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:101
msgid "The request body to send"
msgstr "要发送的请求体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:109
msgid "The response body of the HTTP request"
msgstr "HTTP 请求的响应体"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:114
msgid "HTTP Address"
msgstr "HTTP 地址"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:115
msgid "address"
msgstr "地址"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:117
msgid "The address to send the HTTP request to"
msgstr "发送 HTTP 请求的目标地址"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:122
msgid "Timeout"
msgstr "超时时间"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:127
msgid "The timeout of the HTTP request in seconds"
msgstr "HTTP 请求的超时时间（秒）"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:130
msgid "Token"
msgstr "Token"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:135
msgid "The token to use for the HTTP request"
msgstr "用于 HTTP 请求的 Token"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:138
msgid "Cookies"
msgstr "Cookies"

#: ../packages/dbgpt-core/src/dbgpt/core/awel/trigger/ext_http_trigger.py:143
msgid "The cookies to use for the HTTP request"
msgstr "用于 HTTP 请求的 Cookies"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:42  
msgid "Conversation Composer Operator"  
msgstr "对话编排算子"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:46  
msgid ""  
"A composer operator for conversation.\n"  
"Including chat history handling, prompt composing, etc. Output is "  
"ModelRequest."  
msgstr ""  
"用于对话的编排算子。\n"  
"包括聊天历史处理、提示词编排等。输出为模型请求。"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:51  
msgid "Prompt Template"  
msgstr "提示词模板"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:54  
msgid "The prompt template for the conversation."  
msgstr "对话的提示词模板。"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:57  
#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:215  
msgid "Human Message Key"  
msgstr "人类消息键"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:62  
#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:220  
msgid "The key for human message in the prompt format dict."  
msgstr "提示格式字典中的人类消息键。"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:65  
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:356  
msgid "History Key"  
msgstr "历史记录键"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:71  
msgid ""  
"The chat history key, with chat history message pass to prompt template."  
msgstr "聊天历史记录键，用于将聊天历史记录消息传递给提示模板。"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:76  
msgid "Keep Start Rounds"  
msgstr "保留起始轮次"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:81  
msgid "The start rounds to keep in the chat history."  
msgstr "在聊天历史记录中保留的起始轮次。"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:84  
msgid "Keep End Rounds"  
msgstr "保留结束轮次"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:89  
msgid "The end rounds to keep in the chat history."  
msgstr "在聊天历史记录中保留的结束轮次。"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:92  
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/message_operator.py:130  
msgid "Conversation Storage"  
msgstr "对话存储"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:97  
msgid "The conversation storage(Not include message detail)."  
msgstr "对话存储（不包括消息详情）。"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:100  
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/message_operator.py:141  
msgid "Message Storage"  
msgstr "消息存储"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:105  
msgid "The message storage."  
msgstr "消息存储。"  

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:113  
#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:228  
msgid "The common LLM http request body."  
msgstr "通用的大语言模型 HTTP 请求体。"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:118
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:105
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:208
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:223
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:370
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/message_operator.py:154
msgid "Model Request"
msgstr "模型请求"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:121
msgid "The model request with chat history prompt."
msgstr "包含聊天历史提示的模型请求。"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:207
msgid "Prompt Format Dict Builder Operator"
msgstr "提示格式字典构建算子"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:211
msgid ""
"A operator to build prompt format dict from common LLM http request body."
msgstr "从通用的大语言模型 HTTP 请求体中构建提示格式字典的算子。"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:233
msgid "Prompt Format Dict"
msgstr "提示格式字典"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/composer_operator.py:236
msgid "The prompt format dict."
msgstr "提示格式字典。"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:14
msgid "Merge String to Dict Operator"
msgstr "字符串合并到字典算子"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:18
msgid ""
"Merge two strings to a dict, the fist string which is the value from first "
"upstream is the value of the key `first_key`, the second string which is the "
"value from second upstream is the value of the key `second_key`."
msgstr "将两个字符串合并为一个字典，第一个上游传入的字符串作为键 `first_key` 的值，第二个上游传入的字符串作为键 `second_key` 的值。"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:24
msgid "First Key"
msgstr "第一个键"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:29
msgid "The key for the first string, default is `user_input`."
msgstr "第一个字符串的键，默认是 `user_input`。"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:32
msgid "Second Key"
msgstr "第二个键"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:37
msgid "The key for the second string, default is `context`."
msgstr "第二个字符串的键，默认是 `context`。"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:42
msgid "First String"
msgstr "第一个字符串"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:45
msgid "The first string from first upstream."
msgstr "来自第一个上游的第一个字符串。"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:48
msgid "Second String"
msgstr "第二个字符串"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:51
msgid "The second string from second upstream."
msgstr "来自第二个上游的第二个字符串。"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:56
msgid "Output"
msgstr "输出"

#: ../packages/dbgpt-core/src/dbgpt/core/operators/flow/dict_operator.py:60
msgid "The merged dict. example: {'user_input': 'first', 'context': 'second'}."
msgstr "合并后的字典。示例：{'user_input': 'first', 'context': 'second'}。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:38
msgid "Base Output Operator"
msgstr "基础输出算子"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:42
msgid "The base LLM out parse."
msgstr "基础大语言模型输出解析。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:46
#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:55
#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:308
#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:349
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:456
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:512
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:557
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:608
msgid "Model Output"
msgstr "模型输出"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:50
#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:311
#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:352
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:507
msgid "The model output of upstream."
msgstr "上游的模型输出。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:59
msgid "The model output after parsing."
msgstr "解析后的模型输出。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:301
msgid "SQL Output Parser"
msgstr "SQL 输出解析器"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:304
msgid "Parse the SQL output of an LLM call."
msgstr "解析大语言模型调用的 SQL 输出。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:316
msgid "Dict SQL Output"
msgstr "字典形式的 SQL 输出"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:319
msgid "The dict output after parsing."
msgstr "解析后的字典输出。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:340
msgid "SQL List Output Parser"
msgstr "SQL 列表输出解析器"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:344
msgid "Parse the SQL list output of an LLM call, mostly used for dashboard."
msgstr "解析大语言模型调用的 SQL 列表输出，主要用于仪表板。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:357
msgid "List SQL Output"
msgstr "列表形式的 SQL 输出"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/output_parser.py:361
msgid "The list output after parsing."
msgstr "解析后的列表输出。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/storage.py:391
msgid "Memory Storage"
msgstr "内存存储"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/storage.py:394
msgid "Save your data in memory."
msgstr "将数据保存在内存中。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/storage.py:397
msgid "Serializer"
msgstr "序列化器"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/storage.py:403
msgid ""
"The serializer for serializing the data. If not set, the default JSON "
"serializer will be used."
msgstr "用于序列化数据的序列化器。如果未设置，则使用默认的 JSON 序列化器。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:23
msgid "The name of the model."
msgstr "模型名称。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:31
msgid ""
"The provider of the model. If model is deployed in local, this is the "
"inference type. If model is deployed in third-party service, this is "
"platform name('proxy/<platform>')"
msgstr ""
"模型提供者。若模型部署在本地，此处为推理类型；若模型部署在第三方服务上，此处为平台名称（'proxy/<平台>'）。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:47
msgid "Show verbose output."
msgstr "显示详细输出。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:50
#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:145
#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:163
msgid "Model concurrency limit"
msgstr "模型并发限制"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:83
msgid ""
"The real model name to pass to the provider, default is None. If backend is "
"None, use name as the real model name."
msgstr ""
"传递给模型提供者的实际模型名称，默认为 None。若后端为 None，则使用名称作为实际模型名称。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:93
msgid ""
"Prompt template. If None, the prompt template is automatically determined "
"from model. Just for local deployment."
msgstr ""
"提示模板。若为 None，则提示模板会根据模型自动确定，仅适用于本地部署。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:102
msgid ""
"The context length of the model. If None, it is automatically determined "
"from model."
msgstr "模型的上下文长度。若为 None，则会根据模型自动确定。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:201
msgid ""
"Whether to load the model in 8 bits(LLM.int8() algorithm), default is False."
msgstr "是否以 8 位模式（LLM.int8() 算法）加载模型，默认为 False。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:209
msgid "Whether to load the model in 4 bits, default is False."
msgstr "是否以 4 位模式加载模型，默认为 False。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:262
msgid "Whether to load the model in 8 bits(LLM.int8() algorithm)."
msgstr "是否以 8 位模式（LLM.int8() 算法）加载模型。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:270
msgid ""
"8-bit models can offload weights between the CPU and GPU to support fitting "
"very large models into memory. The weights dispatched to the CPU are "
"actually stored in float32, and aren’t converted to 8-bit. "
msgstr ""
"8 位模型可在 CPU 和 GPU 之间卸载权重，以支持将非常大的模型装入内存。分配给 CPU 的权重实际上以 float32 格式存储，不会转换为 8 位。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:280
msgid ""
"An “outlier” is a hidden state value greater than a certain threshold, and "
"these values are computed in fp16. While the values are usually normally "
"distributed ([-3.5, 3.5]), this distribution can be very different for large "
"models ([-60, 6] or [6, 60]). 8-bit quantization works well for values ~5, "
"but beyond that, there is a significant performance penalty. A good default "
"threshold value is 6, but a lower threshold may be needed for more unstable "
"models (small models or finetuning)."
msgstr ""
"“异常值”是指大于某个阈值的隐藏状态值，这些值以半精度浮点数（fp16）格式计算。通常，这些值呈正态分布（范围为 [-3.5, 3.5]），但对于大模型而言，分布可能有很大差异（如 [-60, 6] 或 [6, 60]）。8 位量化在值约为 5 时效果良好，但超出该范围后，性能会显著下降。一个合理的默认阈值是 6，但对于更不稳定的模型（如小模型或经过微调的模型），可能需要更低的阈值。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:295
msgid ""
"An explicit list of the modules that we do not want to convert in 8-bit. "
"This is useful for models such as Jukebox that has several heads in "
"different places and not necessarily at the last position. For example for "
"`CausalLM` models, the last `lm_head` is kept in its original `dtype`"
msgstr ""
"明确列出我们不想转换为 8 位的模块。这对于像 Jukebox 这样的模型非常有用，这类"
"模型的多个头分布在不同位置，并非一定处于最后位置。例如，对于 `CausalLM` 模"
"型，最后一个 `lm_head` 会保留其原始的数据类型 `dtype`。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:328
msgid "Whether to load the model in 4 bits."
msgstr "是否以 4 位模式加载模型。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:336
msgid ""
"To speedup computation, you can change the data type from float32 (the "
"default value) to bfloat16"
msgstr "为了加速计算，可以将数据类型从 float32（默认值）更改为 bfloat16。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:351
msgid ""
"Quantization datatypes, `fp4` (four bit float) and `nf4` (normal four bit "
"float), only valid when load_4bit=True"
msgstr ""
"量化数据类型，`fp4`（四位浮点数）和 `nf4`（标准四位浮点数），仅在 "
"load_4bit=True 时有效。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:360
msgid ""
"Nested quantization is a technique that can save additional memory at no "
"additional performance cost. This feature performs a second quantization of "
"the already quantized weights to save an additional 0.4 bits/parameter. "
msgstr ""
"嵌套量化是一种可以在不增加性能开销的情况下节省额外内存的技术。此功能对已量化"
"的权重进行二次量化，从而每个参数可额外节省 0.4 位。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:403
msgid "The host IP address to bind to."
msgstr "要绑定的主机 IP 地址。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:406
msgid "The port number to bind to."
msgstr "要绑定的端口号。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:409
msgid "Run the server as a daemon."
msgstr "以守护进程模式运行服务器。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:414
msgid "Logging configuration"
msgstr "日志配置"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/parameter.py:420
msgid "Tracer configuration"
msgstr "追踪器配置"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:53
msgid "Build Model Request"
msgstr "构建模型请求"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:56
msgid "Build the model request from the http request body."
msgstr "从 HTTP 请求体中构建模型请求。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:59
msgid "Default Model Name"
msgstr "默认模型名称"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:64
msgid "The model name of the model request."
msgstr "模型请求中的模型名称。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:67
msgid "Temperature"
msgstr "温度"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:72
msgid "The temperature of the model request."
msgstr "模型请求的温度参数。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:79
msgid "Max New Tokens"
msgstr "最大新 Token 数"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:84
msgid "The max new tokens of the model request."
msgstr "模型请求的最大新 Token 数。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:87
msgid "Context Length"
msgstr "上下文长度"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:92
msgid "The context length of the model request."
msgstr "模型请求的上下文长度。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:100
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:373
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:459
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:552
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:560
msgid "The input value of the operator."
msgstr "算子的输入值。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:108
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:226
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:467
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:612
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:657
msgid "The output value of the operator."
msgstr "算子的输出值。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:201
msgid "Merge Model Request Messages"
msgstr "合并模型请求消息"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:204
msgid "Merge the model request from the input value."
msgstr "从输入值中合并模型请求。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:211
msgid "The model request of upstream."
msgstr "上游的模型请求。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:214
msgid "Model messages"
msgstr "模型消息"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:217
msgid "The model messages of upstream."
msgstr "上游的模型消息。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:362
msgid "LLM Branch Operator"
msgstr "大语言模型分支算子"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:366
msgid "Branch the workflow based on the stream flag of the request."
msgstr "根据请求的流标志对工作流进行分支处理。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:378
msgid "Streaming Model Request"
msgstr "流式模型请求"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:381
msgid "The streaming request, to streaming Operator."
msgstr "流式请求，发送至流式算子。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:384
msgid "Non-Streaming Model Request"
msgstr "非流式模型请求"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:387
msgid "The non-streaming request, to non-streaming Operator."
msgstr "非流式请求，发送至非流式算子。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:449
msgid "Map Model Output to Common Response Body"
msgstr "将模型输出映射到通用响应体"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:452
msgid "Map the model output to the common response body."
msgstr "将模型输出映射到通用响应体。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:464
msgid "Common Response Body"
msgstr "通用响应体"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:495
msgid "Common Streaming Output Operator"
msgstr "通用流式输出算子"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:499
msgid "The common streaming LLM operator, for chat flow."
msgstr "用于聊天流程的通用流式大语言模型算子。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:503
msgid "Upstream Model Output"
msgstr "上游模型输出"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:517
msgid "The model output after transform to common stream format"
msgstr "转换为通用流格式后的模型输出"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:542
msgid "Map String to ModelOutput"
msgstr "将字符串映射到模型输出"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:545
msgid "Map String to ModelOutput."
msgstr "将字符串映射到模型输出。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:549
msgid "String"
msgstr "字符串"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:585
msgid "LLM Branch Join Operator"
msgstr "大语言模型分支合并算子"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:589
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:634
msgid "Just keep the first non-empty output."
msgstr "仅保留第一个非空输出。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:593
msgid "Streaming Model Output"
msgstr "流式模型输出"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:597
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:642
msgid "The streaming output."
msgstr "流式输出。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:600
msgid "Non-Streaming Model Output"
msgstr "非流式模型输出"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:603
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:648
msgid "The non-streaming output."
msgstr "非流式输出。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:630
msgid "String Branch Join Operator"
msgstr "字符串分支合并算子"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:638
msgid "Streaming String Output"
msgstr "流式字符串输出"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:645
msgid "Non-Streaming String Output"
msgstr "非流式字符串输出"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/llm_operator.py:653
msgid "String Output"
msgstr "字符串输出"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/message_operator.py:124
msgid "Chat History Load Operator"
msgstr "聊天历史加载算子"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/message_operator.py:127
msgid "The operator to load chat history from storage."
msgstr "从存储中加载聊天历史的算子。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/message_operator.py:136
msgid ""
"The conversation storage, store the conversation items(Not include message "
"items). If None, we will use InMemoryStorage."
msgstr ""
"对话存储，用于存储对话项（不包括消息项）。如果为 None，则使用 "
"InMemoryStorage。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/message_operator.py:147
msgid ""
"The message storage, store the messages of one conversation. If None, we "
"will use InMemoryStorage."
msgstr ""
"消息存储，用于存储一次对话中的消息。如果为 None，则使用 InMemoryStorage。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/message_operator.py:157
msgid "The model request."
msgstr "模型请求。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/message_operator.py:162
msgid "Stored Messages"
msgstr "已存储的消息"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/message_operator.py:165
msgid "The messages stored in the storage."
msgstr "存储在存储中的消息。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:37
msgid "Common Chat Prompt Template"
msgstr "通用聊天提示模板"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:40
msgid "The operator to build the prompt with static prompt."
msgstr "使用静态提示构建提示的算子。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:44
msgid "System Message"
msgstr "系统消息"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:49
msgid "The system message."
msgstr "系统消息。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:53
msgid "Message placeholder"
msgstr "消息占位符"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:58
msgid "The chat history message placeholder."
msgstr "聊天历史消息占位符。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:61
msgid "Human Message"
msgstr "用户消息"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:67
msgid "The human message."
msgstr "用户消息。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:257
msgid "Prompt Builder Operator"
msgstr "提示构建算子"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:259
msgid "Build messages from prompt template."
msgstr "从提示模板构建消息。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:263
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:350
msgid "Chat Prompt Template"
msgstr "聊天提示模板"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:266
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:353
msgid "The chat prompt template."
msgstr "聊天提示模板。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:271
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:381
msgid "Prompt Input Dict"
msgstr "提示输入字典"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:274
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:384
msgid "The prompt dict."
msgstr "提示输入字典。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:279
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:389
msgid "Formatted Messages"
msgstr "格式化消息"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:283
#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:393
msgid "The formatted messages."
msgstr "格式化消息。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:343
msgid "History Prompt Builder Operator"
msgstr "历史提示构建算子"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:345
msgid "Build messages from prompt template and chat history."
msgstr "从提示模板和聊天历史中构建消息。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:361
msgid "The key of history in prompt dict."
msgstr "提示字典中的历史键。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:364
msgid "String History"
msgstr "字符串历史"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:369
msgid "Whether to convert the history to string."
msgstr "是否将历史转换为字符串。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:374
msgid "History"
msgstr "历史"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/operators/prompt_operator.py:378
msgid "The history."
msgstr "历史记录。"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/llm.py:137
msgid "The media data"
msgstr "媒体数据"

#: ../packages/dbgpt-core/src/dbgpt/core/interface/llm.py:237
msgid "The media object"
msgstr "媒体对象"