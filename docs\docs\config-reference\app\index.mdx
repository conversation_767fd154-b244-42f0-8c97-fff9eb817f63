---
title: "app"
description: "app Configuration"
---

# app Configuration

This document provides an overview of all configuration classes in app type.

import { ConfigClassTable } from '@site/src/components/mdx/ConfigClassTable';

## Configuration Classes

<ConfigClassTable classes={[
  {
    "name": "ChatDashboardConfig",
    "description": "Chat Dashboard Configuration",
    "link": "./config_chatdashboardconfig_2480d0"
  },
  {
    "name": "ChatExcelConfig",
    "description": "Chat Excel Configuration",
    "link": "./config_chatexcelconfig_8dcf86"
  },
  {
    "name": "ChatKnowledgeConfig",
    "description": "Chat Knowledge Configuration",
    "link": "./config_chatknowledgeconfig_d51d9e"
  },
  {
    "name": "ChatNormalConfig",
    "description": "Chat Normal Configuration",
    "link": "./config_chatnormalconfig_83c865"
  },
  {
    "name": "ChatWithDBExecuteConfig",
    "description": "Chat With DB Execute Configuration",
    "link": "./config_chatwithdbexecuteconfig_64fe0f"
  },
  {
    "name": "ChatWithDBQAConfig",
    "description": "Chat With DB QA Configuration",
    "link": "./config_chatwithdbqaconfig_62cbe4"
  },
]} />

