# An example of using docker-compose to start a HA model serving cluster with two controllers and one worker.
# For simplicity, we use chatgpt_proxyllm as the model for the worker, and we build a new docker image named eosphorosai/dbgpt-openai:latest.
# How to build the image:
# run `bash ./docker/base/build_proxy_image.sh` in the root directory of the project.
# If you want to use other pip index url, you can run command with `--pip-index-url` option.
# For example, `bash ./docker/base/build_proxy_image.sh --pip-index-url https://pypi.tuna.tsinghua.edu.cn/simple`
#
# How to start the cluster:
# 1. run `cd docker/compose_examples`
# 2. run `OPENAI_API_KEY="{your api key}" OPENAI_API_BASE="https://api.openai.com/v1" docker compose -f ha-cluster-docker-compose.yml up -d`
# Note: Make sure you have set the environment variables OPENAI_API_KEY.
# Optionally, if you want to use other provider(like proxy/siliconflow), you can set following environment variables:
# LLM_MODEL_PROVIDER="proxy/siliconflow" \
# LLM_MODEL_NAME="Qwen/Qwen2.5-Coder-32B-Instruct" \
# OPENAI_API_BASE="https://api.siliconflow.cn/v1" \
# OPENAI_API_KEY="${SILICONFLOW_API_KEY}" \
# EMBEDDING_MODEL_PROVIDER="proxy/openai" \
# EMBEDDING_MODEL_NAME="BAAI/bge-large-zh-v1.5" \
# EMBEDDING_MODEL_API_URL="https://api.siliconflow.cn/v1/embeddings" \
# docker compose -f ha-cluster-docker-compose.yml up -d
version: '3.10'

services:
  init:
    image: busybox
    volumes:
      - ../examples/sqls:/sqls
      - ../../assets/schema/dbgpt.sql:/dbgpt.sql
      - dbgpt-init-scripts:/docker-entrypoint-initdb.d
    command: /bin/sh -c "cp /dbgpt.sql /docker-entrypoint-initdb.d/ && cp /sqls/* /docker-entrypoint-initdb.d/ && ls /docker-entrypoint-initdb.d/"

  db:
    image: mysql/mysql-server
    environment:
      MYSQL_USER: 'user'
      MYSQL_PASSWORD: 'password'
      MYSQL_ROOT_PASSWORD: 'aa123456'
    ports:
      - 3306:3306
    volumes:
      - dbgpt-myql-db:/var/lib/mysql
      - ../examples/my.cnf:/etc/my.cnf
      - dbgpt-init-scripts:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - dbgptnet
    depends_on:
      - init
  controller-1:
    image: eosphorosai/dbgpt-openai:latest
    # command: python packages/dbgpt-core/src/dbgpt/model/cluster/controller/controller.py -c /root/configs/ha-model-cluster.toml
    command: dbgpt start controller -c /root/configs/ha-model-cluster.toml
    environment:
      - MYSQL_PASSWORD=aa123456
      - MYSQL_HOST=db
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=dbgpt
      - MYSQL_USER=root
    volumes:
      - ../../:/app
      - ./conf/ha-model-cluster.toml:/root/configs/ha-model-cluster.toml
    restart: unless-stopped
    networks:
      - dbgptnet
    depends_on:
      - db
  controller-2:
    image: eosphorosai/dbgpt-openai:latest
    # command: python packages/dbgpt-core/src/dbgpt/model/cluster/controller/controller.py -c /root/configs/ha-model-cluster.toml
    command: dbgpt start controller -c /root/configs/ha-model-cluster.toml
    environment:
      - MYSQL_PASSWORD=aa123456
      - MYSQL_HOST=db
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=dbgpt
      - MYSQL_USER=root
    volumes:
      - ../../:/app
      - ./conf/ha-model-cluster.toml:/root/configs/ha-model-cluster.toml
    restart: unless-stopped
    networks:
      - dbgptnet
    depends_on:
      - db
  llm-worker:
    image: eosphorosai/dbgpt-openai:latest
    # command: python packages/dbgpt-core/src/dbgpt/model/cluster/worker/manager.py -c /root/configs/ha-model-cluster.toml
    command: dbgpt start worker -c /root/configs/ha-model-cluster.toml
    environment:
      - WORKER_TYPE=llm
      - LLM_MODEL_PROVIDER=${LLM_MODEL_PROVIDER:-proxy/openai}
      - LLM_MODEL_NAME=${LLM_MODEL_NAME:-gpt-4o}
      - OPENAI_API_BASE=${OPENAI_API_BASE:-https://api.openai.com/v1}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CONTROLLER_ADDR=http://controller-1:8000,http://controller-2:8000
    depends_on:
      - controller-1
      - controller-2
    volumes:
      - ../../:/app
      - ./conf/ha-model-cluster.toml:/root/configs/ha-model-cluster.toml
    restart: unless-stopped
    networks:
      - dbgptnet
    ipc: host
  embedding-worker:
    image: eosphorosai/dbgpt-openai:latest
    # command: python packages/dbgpt-core/src/dbgpt/model/cluster/worker/manager.py -c /root/configs/ha-model-cluster.toml
    command: dbgpt start worker -c /root/configs/ha-model-cluster.toml
    environment:
      - WORKER_TYPE=text2vec
      - EMBEDDING_MODEL_PROVIDER=${EMBEDDING_MODEL_PROVIDER:-proxy/openai}
      - EMBEDDING_MODEL_NAME=${EMBEDDING_MODEL_NAME:-text-embedding-3-small}
      - EMBEDDING_MODEL_API_URL=${EMBEDDING_MODEL_API_URL:-https://api.openai.com/v1/embeddings}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CONTROLLER_ADDR=http://controller-1:8000,http://controller-2:8000
    depends_on:
      - controller-1
      - controller-2
    volumes:
      - ../../:/app
      - ./conf/ha-model-cluster.toml:/root/configs/ha-model-cluster.toml
    restart: unless-stopped
    networks:
      - dbgptnet
    ipc: host
  webserver:
    image: eosphorosai/dbgpt-openai:latest
    # command: python packages/dbgpt-app/src/dbgpt_app/dbgpt_server.py -c /root/configs/ha-webserver.toml
    command: dbgpt start webserver -c /root/configs/ha-webserver.toml
    environment:
      - MYSQL_PASSWORD=aa123456
      - MYSQL_HOST=db
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=dbgpt
      - MYSQL_USER=root
      - EMBEDDING_MODEL_NAME=${EMBEDDING_MODEL_NAME:-text-embedding-3-small}
      - CONTROLLER_ADDR=http://controller-1:8000,http://controller-2:8000
    depends_on:
      - controller-1
      - controller-2
      - llm-worker
      - embedding-worker
    volumes:
      - ../../:/app
      - ./conf/ha-webserver.toml:/root/configs/ha-webserver.toml
      - dbgpt-data:/app/pilot/data
      - dbgpt-message:/app/pilot/message
    # env_file:
    #   - .env.template
    ports:
      - 5670:5670/tcp
    # webserver may be failed, it must wait all sqls in /docker-entrypoint-initdb.d execute finish.
    restart: unless-stopped
    networks:
      - dbgptnet
  apiserver:
    image: eosphorosai/dbgpt-openai:latest
    command: dbgpt start apiserver -c /root/configs/ha-model-cluster.toml
    environment:
      - CONTROLLER_ADDR=http://controller-1:8000,http://controller-2:8000
    depends_on:
      - controller-1
      - controller-2
      - llm-worker
      - embedding-worker
    volumes:
      - ../../:/app
      - ./conf/ha-model-cluster.toml:/root/configs/ha-model-cluster.toml
    ports:
      - 8100:8100/tcp
    restart: unless-stopped
    networks:
      - dbgptnet
    ipc: host
volumes:
  dbgpt-init-scripts:
  dbgpt-myql-db:
  dbgpt-data:
  dbgpt-message:
networks:
  dbgptnet:
    driver: bridge
    name: dbgptnet