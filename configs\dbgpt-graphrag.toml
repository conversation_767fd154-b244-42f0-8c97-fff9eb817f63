[system]
# Load language from environment variable(It is set by the hook)
language = "${env:DBGPT_LANG:-zh}"
log_level = "INFO"
api_keys = []
encrypt_key = "your_secret_key"

# Server Configurations
[service.web]
host = "0.0.0.0"
port = 5670

[service.web.database]
type = "sqlite"
path = "pilot/meta_data/dbgpt.db"


[rag]
chunk_size=1000
chunk_overlap=0
similarity_top_k=5
similarity_score_threshold=0.0
max_chunks_once_load=10
max_threads=1
rerank_top_k=3

[rag.storage]
[rag.storage.vector]
type = "chroma"
persist_path = "pilot/data"

[rag.storage.graph]
type = "tugraph"
host="127.0.0.1"
port=7687
username="admin"
password="73@TuGraph"

# enable_summary="True"
# community_topk=20
# community_score_threshold=0.3

# triplet_graph_enabled="True"
# extract_topk=20

# document_graph_enabled="True"
# knowledge_graph_chunk_search_top_size=20
# knowledge_graph_extraction_batch_size=20

# enable_similarity_search="True"
# knowledge_graph_embedding_batch_size=20
# similarity_search_topk=5
# extract_score_threshold=0.7

# enable_text_search="True"
# text2gql_model_enabled="True"
# text2gql_model_name="qwen2.5:latest"



# Model Configurations
[models]
[[models.llms]]
name = "${env:LLM_MODEL_NAME:-gpt-4o}"
provider = "${env:LLM_MODEL_PROVIDER:-proxy/openai}"
api_base = "${env:OPENAI_API_BASE:-https://api.openai.com/v1}"
api_key = "${env:OPENAI_API_KEY}"

[[models.embeddings]]
name = "${env:EMBEDDING_MODEL_NAME:-text-embedding-3-small}"
provider = "${env:EMBEDDING_MODEL_PROVIDER:-proxy/openai}"
api_url = "${env:EMBEDDING_MODEL_API_URL:-https://api.openai.com/v1/embeddings}"
api_key = "${env:OPENAI_API_KEY}"
