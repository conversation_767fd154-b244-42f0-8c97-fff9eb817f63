# Chat Data

Chat data capability is to dialogue with data through natural language. Currently, it is mainly dialogue between structured and semi-structured data, which can assist in data analysis and insight.

:::info note

Before starting the data conversation, we first need to add the data source
:::

## steps

To start a data conversation, you need to go through the following steps:
- 1.Add data source
- 2.Select ChatData
- 3.Select the corresponding database
- 4.Start a conversation

### Add data source

First, select the [data source](../datasources.md) on the left to add and add a database. Currently, DB-GPT supports multiple database types. Just select the corresponding database type to add. Here we choose MySQL as a demonstration. For the test data of the demonstration, see the [test sample](https://github.com/eosphoros-ai/DB-GPT/tree/main/docker/examples/sqls).


### Choose ChatData App

<p align="center">
  <img src={'/img/app/chat_data_v0.6.jpg'} width="800px" />
</p>

### Start a conversation

<p align="center">
  <img src={'/img/app/chat_data_display_v0.6.jpg'} width="800px" />
</p>



