---
title: "embedding"
description: "embedding Configuration"
---

# embedding Configuration

This document provides an overview of all configuration classes in embedding type.

import { ConfigClassTable } from '@site/src/components/mdx/ConfigClassTable';

## Configuration Classes

<ConfigClassTable classes={[
  {
    "name": "HFEmbeddingDeployModelParameters",
    "description": "HFEmbeddingDeployModelParameters(name: str, provider: str = 'hf', verbose: Optional[bool] = False, concurrency: Optional[int] = 100, path: Optional[str] = None, device: Optional[str] = None, cache_folder: Optional[str] = None, normalize_embeddings: bool = False, multi_process: bool = False, model_kwargs: Dict[str, Any] = <factory>, encode_kwargs: Dict[str, Any] = <factory>, embed_instruction: Optional[str] = None, query_instruction: Optional[str] = None)",
    "link": "./embeddings_hfembeddingdeploymodelparameters_f588e1"
  },
  {
    "name": "JinaEmbeddingsDeployModelParameters",
    "description": "Jina AI Embeddings deploy model parameters.",
    "link": "./jina_jinaembeddingsdeploymodelparameters_40b0f2"
  },
  {
    "name": "OllamaEmbeddingDeployModelParameters",
    "description": "Ollama Embeddings deploy model parameters.",
    "link": "./ollama_ollamaembeddingdeploymodelparameters_b511e0"
  },
  {
    "name": "OpenAPIEmbeddingDeployModelParameters",
    "description": "OpenAPI embedding deploy model parameters.",
    "link": "./embeddings_openapiembeddingdeploymodelparameters_f9ba47"
  },
  {
    "name": "QianfanEmbeddingDeployModelParameters",
    "description": "Qianfan Embeddings deploy model parameters.",
    "link": "./qianfan_qianfanembeddingdeploymodelparameters_257d2a"
  },
  {
    "name": "TongyiEmbeddingDeployModelParameters",
    "description": "Qianfan Embeddings deploy model parameters.",
    "link": "./tongyi_tongyiembeddingdeploymodelparameters_a7cbb4"
  },
]} />

