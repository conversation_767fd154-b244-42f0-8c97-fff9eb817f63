---
title: "SiliconFlowRerankEmbeddingsParameters Configuration"
description: "SiliconFlow Rerank Embeddings Parameters."
---

import { ConfigDetail } from "@site/src/components/mdx/ConfigDetail";

<ConfigDetail config={{
  "name": "SiliconFlowRerankEmbeddingsParameters",
  "description": "SiliconFlow Rerank Embeddings Parameters.",
  "documentationUrl": "",
  "parameters": [
    {
      "name": "name",
      "type": "string",
      "required": true,
      "description": "The name of the model."
    },
    {
      "name": "provider",
      "type": "string",
      "required": false,
      "description": "The provider of the model. If model is deployed in local, this is the inference type. If model is deployed in third-party service, this is platform name('proxy/<platform>')",
      "defaultValue": "proxy/siliconflow"
    },
    {
      "name": "verbose",
      "type": "boolean",
      "required": false,
      "description": "Show verbose output.",
      "defaultValue": "False"
    },
    {
      "name": "concurrency",
      "type": "integer",
      "required": false,
      "description": "Model concurrency limit",
      "defaultValue": "50"
    },
    {
      "name": "api_url",
      "type": "string",
      "required": false,
      "description": "The URL of the rerank API.",
      "defaultValue": "https://api.siliconflow.cn/v1/rerank"
    },
    {
      "name": "api_key",
      "type": "string",
      "required": false,
      "description": "The API key for the rerank API.",
      "defaultValue": "${env:SILICONFLOW_API_KEY}"
    },
    {
      "name": "backend",
      "type": "string",
      "required": false,
      "description": "The real model name to pass to the provider, default is None. If backend is None, use name as the real model name."
    },
    {
      "name": "timeout",
      "type": "integer",
      "required": false,
      "description": "The timeout for the request in seconds.",
      "defaultValue": "60"
    }
  ]
}} />

