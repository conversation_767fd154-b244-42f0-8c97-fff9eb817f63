CREATE SCHEMA case_2_ecom;
COMMENT ON SCHEMA case_2_ecom is '电子商务系统';

SET SEARCH_PATH = case_2_ecom;

CREATE TABLE users (
    user_id INT PRIMARY KEY,
    user_name VARCHAR(100),
    user_email VARCHAR(100),
    registration_date DATE,
    user_country VARCHAR(100)
);
COMMENT ON TABLE users IS '用户信息表';
COMMENT ON COLUMN users.user_name IS '用户名';
COMMENT ON COLUMN users.user_email IS '用户邮箱';
COMMENT ON COLUMN users.registration_date IS '注册日期';
COMMENT ON COLUMN users.user_country  IS '用户国家';

CREATE TABLE products (
    product_id INT PRIMARY KEY,
    product_name VARCHAR(100),
    product_price FLOAT
);
COMMENT ON TABLE products IS '商品信息表';
COMMENT ON COLUMN products.product_name IS '商品名称';
COMMENT ON COLUMN products.product_price IS '商品价格';

CREATE TABLE orders (
    order_id INT PRIMARY KEY,
    user_id INT,
    product_id INT,
    quantity INT,
    order_date DATE,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (product_id) REFERENCES products(product_id)
);
COMMENT ON TABLE orders IS '订单信息表';
COMMENT ON COLUMN orders.quantity IS '数量';
COMMENT ON COLUMN orders.order_date IS '订单日期';


INSERT INTO users (user_id, user_name, user_email, registration_date, user_country) VALUES
(1, 'John', '<EMAIL>', '2020-01-01', 'USA'),
(2, 'Mary', '<EMAIL>', '2021-01-01', 'UK'),
(3, 'Bob', '<EMAIL>', '2020-01-01', 'USA'),
(4, 'Alice', '<EMAIL>', '2021-01-01', 'UK'),
(5, 'Charlie', '<EMAIL>', '2020-01-01', 'USA'),
(6, 'David', '<EMAIL>', '2021-01-01', 'UK'),
(7, 'Eve', '<EMAIL>', '2020-01-01', 'USA'),
(8, 'Frank', '<EMAIL>', '2021-01-01', 'UK'),
(9, 'Grace', '<EMAIL>', '2020-01-01', 'USA'),
(10, 'Helen', '<EMAIL>', '2021-01-01', 'UK');

INSERT INTO products (product_id, product_name, product_price) VALUES
(1, 'iPhone', 699),
(2, 'Samsung Galaxy', 599),
(3, 'iPad', 329),
(4, 'Macbook', 1299),
(5, 'Apple Watch', 399),
(6, 'AirPods', 159),
(7, 'Echo', 99),
(8, 'Kindle', 89),
(9, 'Fire TV Stick', 39),
(10, 'Echo Dot', 49);

INSERT INTO orders (order_id, user_id, product_id, quantity, order_date) VALUES
(1, 1, 1, 1, '2022-01-01'),
(2, 1, 2, 1, '2022-02-01'),
(3, 2, 3, 2, '2022-03-01'),
(4, 2, 4, 1, '2022-04-01'),
(5, 3, 5, 2, '2022-05-01'),
(6, 3, 6, 3, '2022-06-01'),
(7, 4, 7, 2, '2022-07-01'),
(8, 4, 8, 1, '2022-08-01'),
(9, 5, 9, 2, '2022-09-01'),
(10, 5, 10, 3, '2022-10-01');

COMMIT;
