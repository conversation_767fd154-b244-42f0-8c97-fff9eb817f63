---
title: "memory"
description: "memory Configuration"
---

# memory Configuration

This document provides an overview of all configuration classes in memory type.

import { ConfigClassTable } from '@site/src/components/mdx/ConfigClassTable';

## Configuration Classes

<ConfigClassTable classes={[
  {
    "name": "BufferWindowGPTsAppMemoryConfig",
    "description": "Buffer window memory configuration.\n\n    This configuration is used to control the buffer window memory.",
    "link": "./config_bufferwindowgptsappmemoryconfig_c31071"
  },
  {
    "name": "TokenBufferGPTsAppMemoryConfig",
    "description": "Token buffer memory configuration.\n\n    This configuration is used to control the token buffer memory.",
    "link": "./config_tokenbuffergptsappmemoryconfig_6a2000"
  },
]} />

