---
title: "ServeConfig Configuration"
description: "Parameters for the serve command"
---

import { ConfigDetail } from "@site/src/components/mdx/ConfigDetail";

<ConfigDetail config={{
  "name": "ServeConfig",
  "description": "Parameters for the serve command",
  "documentationUrl": "",
  "parameters": [
    {
      "name": "api_keys",
      "type": "string",
      "required": false,
      "description": "API keys for the endpoint, if None, allow all"
    }
  ]
}} />

