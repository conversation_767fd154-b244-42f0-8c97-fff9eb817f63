[system]
# Load language from environment variable(It is set by the hook)
language = "${env:DBGPT_LANG:-en}"
log_level = "INFO"
api_keys = []
encrypt_key = "your_secret_key"

# Server Configurations
[service.web]
host = "0.0.0.0"
port = 5670
light = true
controller_addr = "${env:CONTROLLER_ADDR}"

[service.web.database]
type = "mysql"
host = "${env:MYSQL_HOST}"
port = "${env:MYSQL_PORT}"
database = "${env:MYSQL_DATABASE}"
user = "${env:MYSQL_USER}"
password ="${env:MYSQL_PASSWORD}"


[models]
default_embedding = "${env:EMBEDDING_MODEL_NAME:-text-embedding-3-small}"

[log]
level = "INFO"