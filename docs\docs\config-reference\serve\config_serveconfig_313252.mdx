---
title: "Conversation Serve Configurations Configuration"
description: "This configuration is for the conversation serve module."
---

import { ConfigDetail } from "@site/src/components/mdx/ConfigDetail";

<ConfigDetail config={{
  "name": "ServeConfig",
  "description": "This configuration is for the conversation serve module.",
  "documentationUrl": null,
  "parameters": [
    {
      "name": "api_keys",
      "type": "string",
      "required": false,
      "description": "API keys for the endpoint, if None, allow all"
    },
    {
      "name": "default_model",
      "type": "string",
      "required": false,
      "description": "Default model for the conversation"
    }
  ]
}} />

