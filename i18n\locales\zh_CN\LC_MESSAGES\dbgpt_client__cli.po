# Chinese translations for PACKAGE package
# PACKAGE 软件包的简体中文翻译.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-23 13:40+0800\n"
"PO-Revision-Date: 2025-02-23 13:40+0800\n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:45
msgid "The path of the AWEL flow"
msgstr "AWEL 工作流的路径"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:63
msgid "The name of the AWEL flow"
msgstr "AWEL 工作流的名称"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:70
msgid "The uid of the AWEL flow"
msgstr "AWEL 工作流的 UID"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:88
msgid "The messages to run AWEL flow"
msgstr "用于运行 AWEL 工作流的消息"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:95
msgid "The model name of AWEL flow"
msgstr "AWEL 工作流的模型名称"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:104
msgid "Whether use stream mode to run AWEL flow"
msgstr "是否使用流模式运行 AWEL 工作流"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:112
msgid "The temperature to run AWEL flow"
msgstr "运行 AWEL 工作流的温度参数"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:119
msgid "The max new tokens to run AWEL flow"
msgstr "运行 AWEL 工作流的最大新 Token 数"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:126
msgid "The conversation id of the AWEL flow"
msgstr "AWEL 工作流的对话 ID"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:134
#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:199
msgid "The json data to run AWEL flow, if set, will overwrite other options"
msgstr "用于运行 AWEL 工作流的 JSON 数据，如果设置，将覆盖其他选项"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:142
msgid "The extra json data to run AWEL flow."
msgstr "用于运行 AWEL 工作流的额外 JSON 数据。"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:151
msgid "Whether use interactive mode to run AWEL flow"
msgstr "是否使用交互模式运行 AWEL 工作流"

#: ../packages/dbgpt-client/src/dbgpt_client/_cli.py:207
msgid ""
"The output key of the AWEL flow, if set, it will try to get the output by "
"the key"
msgstr "AWEL 工作流的输出键，若设置了该键，则会尝试通过它来获取输出"