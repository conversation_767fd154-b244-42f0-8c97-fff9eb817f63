FROM node:lts-alpine as build

RUN apk add --no-cache git

ARG NPM_REGISTRY=https://registry.npmjs.org
ENV NPM_REGISTRY=$NPM_REGISTRY

# Set github CI environment variable
ARG CI=true
ENV CI=$CI

WORKDIR /app

# Copy package.json and package-lock.json to a separate build directory
COPY docs/package*.json /app-build/docs/

# Install dependencies in the separate build directory
RUN cd /app-build/docs && \
    npm config set registry $NPM_REGISTRY && \
    npm ci

# Copy the rest of the application to /app and /app-build
COPY . /app-build
COPY . /app

# Make sure we have the latest version of the repository
RUN if [ "$CI" = "true" ]; then \
        # Check if the repo is shallow before trying to unshallow it
        if git rev-parse --is-shallow-repository | grep -q 'true'; then \
            git fetch --prune --unshallow; \
        else \
            echo "Repository is already complete, skipping unshallow"; \
            git fetch --prune; \
        fi; \
    fi

ARG NUM_VERSION=2
ENV NUM_VERSION=$NUM_VERSION

# Commit the changes to the repository, just for local testing
# Sometimes, we just want to test the changes in the Dockerfile
RUN git config --global user.email "<EMAIL>" && \
    git config --global user.name "DB-GPT" && \
    git add . && git commit --no-verify -m "Commit message" || exit 1

# New logic for building versions directly in Dockerfile
RUN git config --global --add safe.directory /app && \
    # Record the current position
    CURRENT_POSITION=$(git rev-parse --abbrev-ref HEAD) && \
    # Get the latest tags
    TAGS=$(git tag --sort=-creatordate | head -n $NUM_VERSION | tac) && \
    # If there are no tags, get the latest commits
    if [ -z "$TAGS" ]; then \
        TAGS=$(git log --format="%h" -n $NUM_VERSION | tac); \
    fi && \
    for TAG in $TAGS; do \
        echo "Creating version $TAG"; \
        cd /app/docs && git checkout . && git checkout $TAG; \
        echo "Checked out to tag: $TAG"; \
        # Check if there is a patch for the current version in app-build
        echo "Checking patch in /app-build/docs/patchs..." && \
        if [ -f "/app-build/docs/patchs/fix_${TAG/v/}.patch" ]; then \
            echo "Found patch for version $TAG in /app-build/docs/patchs, applying..."; \
            cd /app && \
            git apply "/app-build/docs/patchs/fix_${TAG/v/}.patch" && \
            echo "Patch applied successfully" || \
            echo "Failed to apply patch for $TAG"; \
            echo "Current sidebars.js content:"; \
            cat /app/docs/sidebars.js; \
        else \
            echo "No patch found for $TAG in /app-build/docs/patchs"; \
        fi; \
        # Copy the necessary files to the build directory for each tag
        rm -rf /app-build/docs/docs /app-build/docs/sidebars.js /app-build/docs/static /app-build/docs/src && \
        cp -r /app/docs/docs /app-build/docs/ && \
        cp /app/docs/sidebars.js /app-build/docs/ && \
        cp -r /app/docs/static /app-build/docs/ && \
        cp -r /app/docs/src /app-build/docs/; \
        # Create a new version
        cd /app-build/docs && npm run docusaurus docs:version $TAG || exit 1; \
    done && \
    # Return to the original position, build dev version
    cd /app && git checkout . && git checkout $CURRENT_POSITION && \
    cd /app/docs && \
    rm -rf /app-build/docs/docs /app-build/docs/sidebars.js /app-build/docs/static /app-build/docs/src && \
    cp -r /app/docs/docs /app-build/docs/ && \
    cp /app/docs/sidebars.js /app-build/docs/ && \
    cp -r /app/docs/static /app-build/docs/ && \
    cp -r /app/docs/src /app-build/docs/ || exit 1; \
    cd /app-build/docs && npm run build && \
    echo $TAGS | tr ' ' '\n' | tac > /app-build/docs/build/versions.txt && \
    echo "latest" >> /app-build/docs/build/versions.txt && \
    echo "Built versions:" && \
    cat /app-build/docs/build/versions.txt

# For production
FROM nginx:alpine

# Copy the nginx configuration file
# COPY nginx.conf /etc/nginx/nginx.conf

# Copy the build output to replace the default nginx contents.
COPY --from=build /app-build/docs/build /usr/share/nginx/html
COPY --from=build /app-build/docs/versioned_docs/ /usr/share/nginx/html/versioned_docs/
COPY --from=build /app-build/docs/versioned_sidebars/ /usr/share/nginx/html/versioned_sidebars/

RUN echo '#!/bin/sh' > /usr/share/nginx/html/versions.sh && \
    echo 'echo "Available versions:"' >> /usr/share/nginx/html/versions.sh && \
    echo 'cat /usr/share/nginx/html/versions.txt' >> /usr/share/nginx/html/versions.sh && \
    chmod +x /usr/share/nginx/html/versions.sh

EXPOSE 80

# Start Nginx server
CMD ["nginx", "-g", "daemon off;"]