---
title: "graph_store"
description: "graph_store Configuration"
---

# graph_store Configuration

This document provides an overview of all configuration classes in graph_store type.

import { ConfigClassTable } from '@site/src/components/mdx/ConfigClassTable';

## Configuration Classes

<ConfigClassTable classes={[
  {
    "name": "BuiltinKnowledgeGraphConfig",
    "description": "",
    "link": "./knowledge_graph_builtinknowledgegraphconfig_f26e05"
  },
  {
    "name": "Neo4jStoreConfig",
    "description": "",
    "link": "./neo4j_store_neo4jstoreconfig_a4db5d"
  },
  {
    "name": "OpenSPGConfig",
    "description": "",
    "link": "./open_spg_openspgconfig_a744fd"
  },
  {
    "name": "TuGraphStoreConfig",
    "description": "TuGraph store config.",
    "link": "./tugraph_store_tugraphstoreconfig_7ca8a8"
  },
]} />

