---
title: "ApplicationConfig Configuration"
description: "Application configuration."
---

import { ConfigDetail } from "@site/src/components/mdx/ConfigDetail";

<ConfigDetail config={{
  "name": "ApplicationConfig",
  "description": "Application configuration.",
  "documentationUrl": "",
  "parameters": [
    {
      "name": "hooks",
      "type": "HookConfig",
      "required": false,
      "description": "Configuration hooks, which will be executed before the configuration loading",
      "nestedTypes": [
        {
          "type": "link",
          "text": "hookconfig configuration",
          "url": "manager_hookconfig_d9a481"
        }
      ],
      "defaultValue": "[]"
    },
    {
      "name": "system",
      "type": "SystemParameters",
      "required": false,
      "description": "System configuration",
      "nestedTypes": [
        {
          "type": "link",
          "text": "systemparameters configuration",
          "url": "config_systemparameters_fd92b9"
        }
      ],
      "defaultValue": "SystemParameters"
    },
    {
      "name": "service",
      "type": "ServiceConfig",
      "required": false,
      "description": "",
      "nestedTypes": [
        {
          "type": "link",
          "text": "serviceconfig configuration",
          "url": "service/config_serviceconfig_81a10f"
        }
      ],
      "defaultValue": "ServiceConfig"
    },
    {
      "name": "models",
      "type": "ModelsDeployParameters",
      "required": false,
      "description": "Model deployment configuration",
      "nestedTypes": [
        {
          "type": "link",
          "text": "modelsdeployparameters configuration",
          "url": "service/parameter_modelsdeployparameters_5c7bc5"
        }
      ],
      "defaultValue": "ModelsDeployParameters"
    },
    {
      "name": "serves",
      "type": "BaseServeConfig",
      "required": false,
      "description": "Serve configuration",
      "nestedTypes": [
        {
          "type": "link",
          "text": "datasource configuration",
          "url": "serve/config_serveconfig_63f1e9"
        },
        {
          "type": "link",
          "text": "agent/chat configuration",
          "url": "serve/config_serveconfig_adbd6f"
        },
        {
          "type": "link",
          "text": "conversation configuration",
          "url": "serve/config_serveconfig_313252"
        },
        {
          "type": "link",
          "text": "dbgpts_hub configuration",
          "url": "serve/config_serveconfig_ec2d70"
        },
        {
          "type": "link",
          "text": "dbgpts_my configuration",
          "url": "serve/config_serveconfig_1a9284"
        },
        {
          "type": "link",
          "text": "evaluate configuration",
          "url": "serve/config_serveconfig_8839e0"
        },
        {
          "type": "link",
          "text": "feedback configuration",
          "url": "serve/config_serveconfig_fa1f35"
        },
        {
          "type": "link",
          "text": "file configuration",
          "url": "serve/config_serveconfig_cb64c6"
        },
        {
          "type": "link",
          "text": "flow configuration",
          "url": "serve/config_serveconfig_c0b589"
        },
        {
          "type": "link",
          "text": "libro configuration",
          "url": "serve/config_serveconfig_b1c2b9"
        },
        {
          "type": "link",
          "text": "model configuration",
          "url": "serve/config_serveconfig_7a0577"
        },
        {
          "type": "link",
          "text": "prompt configuration",
          "url": "serve/config_serveconfig_854dad"
        },
        {
          "type": "link",
          "text": "rag configuration",
          "url": "serve/config_serveconfig_7889f9"
        }
      ],
      "defaultValue": "[]"
    },
    {
      "name": "rag",
      "type": "RagParameters",
      "required": false,
      "description": "Rag Knowledge Parameters",
      "nestedTypes": [
        {
          "type": "link",
          "text": "ragparameters configuration",
          "url": "app/config_ragparameters_7483b2"
        }
      ],
      "defaultValue": "RagParameters"
    },
    {
      "name": "app",
      "type": "GPTsAppConfig",
      "required": false,
      "description": "GPTs application configuration",
      "nestedTypes": [
        {
          "type": "link",
          "text": "gptsappconfig configuration",
          "url": "app/config_gptsappconfig_134d16"
        }
      ],
      "defaultValue": "GPTsAppConfig"
    },
    {
      "name": "trace",
      "type": "TracerParameters",
      "required": false,
      "description": "Global tracer configuration",
      "nestedTypes": [
        {
          "type": "link",
          "text": "tracerparameters configuration",
          "url": "utils/tracer_impl_tracerparameters_f8f272"
        }
      ],
      "defaultValue": "TracerParameters"
    },
    {
      "name": "log",
      "type": "LoggingParameters",
      "required": false,
      "description": "Logging configuration",
      "nestedTypes": [
        {
          "type": "link",
          "text": "loggingparameters configuration",
          "url": "utils/utils_loggingparameters_4ba5c6"
        }
      ],
      "defaultValue": "LoggingParameters"
    }
  ]
}} />

