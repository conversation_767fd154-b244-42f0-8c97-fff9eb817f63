# 3.2 Handling GET Requests

In previous section, we created a simple HTTP trigger that returns a fixed string. In 
this section, we will create a new HTTP trigger that returns a string based on the 
query parameters of the request.

## Say Hello To Someone

Before we start writing the code, we need to install the `pydantic` package in your 
project [awel-tutorial](/docs/awel/awel_tutorial/getting_started/1.1_hello_world#creating-a-projec)

```bash
poetry add "pydantic>=2.6.0"
```

Then create a new file named `http_trigger_say_hello.py` in the `awel_tutorial` directory and add the following code:

```python
from dbgpt._private.pydantic import BaseModel, Field
from dbgpt.core.awel import DAG, HttpTrigger, MapOperator, setup_dev_environment

class TriggerReqBody(BaseModel):
    name: str = Field(..., description="User name")
    age: int = Field(18, description="User age")

with DAG("awel_say_hello") as dag:
    trigger_task = HttpTrigger(
        endpoint="/awel_tutorial/say_hello", 
        methods="GET", 
        request_body=TriggerReqBody,
        status_code=200
    )
    task = MapOperator(
        map_function=lambda x: f"Hello, {x.name}! You are {x.age} years old."
    )
    trigger_task >> task

setup_dev_environment([dag], port=5555)
```

And run the following command to execute the code:

```bash
poetry run python awel_tutorial/http_trigger_say_hello.py
```

Now, open a new terminal and run the following command to send a GET request to the server:

```bash
curl -X GET \
"http://127.0.0.1:5555/api/v1/awel/trigger/awel_tutorial/say_hello?name=John&age=25"
```

The output should look like this:

```plaintext
"Hello, John! You are 25 years old."
```

Then you can stop the server by pressing `Ctrl+C`.

In above code, we created a `TriggerReqBody` class that inherits from `BaseModel` to 
define the structure of the request body. The module `dbgpt._private.pydantic` wraps 
the pydantic for the compatibility of different versions of pydantic.

When receiving a request, http trigger will parse the query parameters and build a 
`TriggerReqBody` object, then pass it to the next operator.

## Return JSON Response

In previous section, we returned a string as the response. And we can also return a 
JSON response.

AWEL use `fastapi` as the default web framework, and it will automatically judge the
response type of your task output,  common `dict`, `list`, `BaseModel` of `pydantic`, 
etc. will be automatically converted to JSON response.

So if you want to return a JSON response, you can simply return a `dict` or a `BaseModel`.

Create a new file named `http_trigger_say_hello_json.py` in the `awel_tutorial` directory and add the following code:

```python
from dbgpt._private.pydantic import BaseModel, Field
from dbgpt.core.awel import DAG, HttpTrigger, MapOperator, setup_dev_environment

class TriggerReqBody(BaseModel):
    name: str = Field(..., description="User name")
    age: int = Field(18, description="User age")

with DAG("awel_say_hello_json") as dag:
    trigger_task = HttpTrigger(
        endpoint="/awel_tutorial/say_hello_json", 
        methods="GET", 
        request_body=TriggerReqBody,
    )
    task = MapOperator(
        map_function=lambda x: {"message": f"Hello, {x.name}! You are {x.age} years old."}
    )
    trigger_task >> task

setup_dev_environment([dag], port=5555)
```

And run the following command to execute the code:

```bash
poetry run python awel_tutorial/http_trigger_say_hello_json.py
```

Now, open a new terminal and run the following command to send a GET request to the server:

```bash
curl -X GET \
"http://127.0.0.1:5555/api/v1/awel/trigger/awel_tutorial/say_hello_json?name=John&age=25"
```

And you will get the following output:

```plaintext
{"message":"Hello, John! You are 25 years old."}
```

Then you can stop the server by pressing `Ctrl+C`.