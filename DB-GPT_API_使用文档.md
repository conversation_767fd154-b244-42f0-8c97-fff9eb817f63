# DB-GPT API 使用文档

## 概述

DB-GPT 提供了丰富的 API 接口，主要分为两大类：
1. **模型 API** - 兼容 OpenAI SDK 标准的模型服务接口
2. **应用服务层 API** - DB-GPT 应用层功能接口

## 1. 认证方式

所有 API 请求都需要在 HTTP 头中包含 API 密钥：

```http
Authorization: Bearer DBGPT_API_KEY
```

### 配置 API 密钥

在 `.env` 文件中设置：
```bash
API_KEYS=dbgpt
```

## 2. 模型 API

### 2.1 概述
DB-GPT 通过服务导向的多模型管理框架(SMMF)，提供兼容 OpenAI SDK 的模型服务。

### 2.2 基础配置
- **默认端口**: 8100
- **API 基础路径**: `http://127.0.0.1:8100/api/v1`

### 2.3 使用 OpenAI SDK 调用

```python
import openai

model = "Qwen/QwQ-32B"

client = openai.OpenAI(
    api_key="EMPTY",
    base_url="http://127.0.0.1:8100/api/v1",
)

completion = client.chat.completions.create(
    model=model,
    messages=[{"role": "user", "content": "hello"}]
)

print(completion.choices[0].message.content)
```

### 2.4 主要端点

- `GET /v1/models` - 获取可用模型列表
- `POST /v1/chat/completions` - 聊天完成接口

## 3. 应用服务层 API

### 3.1 概述
- **默认端口**: 5670
- **API 基础路径**: `http://127.0.0.1:5670/api`
- **文档地址**: `http://127.0.0.1:5670/docs`

### 3.2 API 版本说明
- `/api/v1/` - 第一版本 API
- `/api/v2/` - 第二版本 API

## 4. DB-GPT Python 客户端

### 4.1 安装

```bash
pip install "dbgpt-client>=0.7.1rc0"
```

### 4.2 基础使用

```python
from dbgpt_client import Client

DBGPT_API_KEY = "dbgpt"
DBGPT_API_BASE = "http://localhost:5670/api/v2"

client = Client(api_base=DBGPT_API_BASE, api_key=DBGPT_API_KEY)
```

### 4.3 聊天接口

#### 普通聊天
```python
# 同步聊天
res = await client.chat(model="chatgpt_proxyllm", messages="Hello?")
print(res.json())

# 流式聊天
async for data in client.chat_stream(
    model="chatgpt_proxyllm",
    messages="hello",
):
    print(data.dict())
```

#### 应用聊天
```python
async for data in client.chat_stream(
    model="chatgpt_proxyllm",
    chat_mode="chat_app",
    chat_param="${app_code}",
    messages="hello",
):
    print(data.dict())
```

#### 知识库聊天
```python
async for data in client.chat_stream(
    model="chatgpt_proxyllm",
    chat_mode="chat_knowledge",
    chat_param="${space_name}",
    messages="hello",
):
    print(data.dict())
```

#### 工作流聊天
```python
async for data in client.chat_stream(
    model="chatgpt_proxyllm",
    chat_mode="chat_flow",
    chat_param="${flow_id}",
    messages="hello",
):
    print(data.dict())
```

## 5. 主要 API 分类

### 5.1 聊天 API (Chat API)
```
api/v1/chat/db/list              # 数据库连接列表
api/v1/chat/db/add               # 添加数据库连接
api/v1/chat/db/edit              # 编辑数据库连接
api/v1/chat/db/delete            # 删除数据库连接
api/v1/chat/db/test/connect      # 测试数据库连接
api/v1/chat/db/summary           # 数据库摘要
api/v1/chat/db/support/type      # 支持的数据库类型
api/v1/chat/dialogue/list        # 对话列表
api/v1/chat/dialogue/scenes      # 对话场景
api/v1/chat/dialogue/new         # 新建对话
api/v1/chat/mode/params/list     # 聊天模式参数列表
api/v1/chat/mode/params/file/load # 加载参数文件
api/v1/chat/dialogue/delete      # 删除对话
api/v1/chat/dialogue/messages    # 对话消息
api/v1/chat/prepare              # 聊天准备
api/v1/chat/completions          # 聊天完成
```

### 5.2 编辑器 API (Editor API)
```
api/v1/editor/db/tables          # 数据库表列表
api/v1/editor/sql/rounds         # SQL 轮次
api/v1/editor/sql                # SQL 编辑
api/v1/editor/sql/run            # 运行 SQL
api/v1/sql/editor/submit         # 提交 SQL 编辑
api/v1/editor/chart/list         # 图表列表
api/v1/editor/chart/info         # 图表信息
api/v1/editor/chart/run          # 运行图表
api/v1/chart/editor/submit       # 提交图表编辑
```

### 5.3 模型管理 API (Model API)
```
api/v1/model/types               # 模型类型
api/v1/model/supports            # 支持的模型
```

### 5.4 LLM 管理 API
```
api/v1/worker/model/params       # 模型参数
api/v1/worker/model/list         # 模型列表
api/v1/worker/model/stop         # 停止模型
api/v1/worker/model/start        # 启动模型
api/worker/generate_stream       # 流式生成
api/worker/generate              # 生成
api/worker/embeddings            # 嵌入
api/worker/apply                 # 应用
api/worker/parameter/descriptions # 参数描述
api/worker/models/supports       # 支持的模型
api/worker/models/startup        # 模型启动
api/worker/models/shutdown       # 模型关闭
api/controller/models            # 控制器模型
api/controller/heartbeat         # 心跳检测
```

### 5.5 Agent API
```
api/v1/agent/hub/update          # 更新 Agent Hub
api/v1/agent/query               # 查询 Agent
api/v1/agent/my                  # 我的 Agent
api/v1/agent/install             # 安装 Agent
api/v1/agent/uninstall           # 卸载 Agent
api/v1/personal/agent/upload     # 上传个人 Agent
```

### 5.6 AWEL API
```
api/v1/awel/trigger/examples/simple_rag  # 简单 RAG 示例
api/v1/awel/trigger/examples/simple_chat # 简单聊天示例
api/v1/awel/trigger/examples/hello       # Hello 示例
```

## 6. 数据源 API 示例

### 6.1 使用 cURL
```bash
DBGPT_API_KEY=dbgpt
DB_NAME="{your_db_name}"

curl -X POST "http://localhost:5670/api/v2/chat/completions" \
    -H "Authorization: Bearer $DBGPT_API_KEY" \
    -H "accept: application/json" \
    -H "Content-Type: application/json" \
    -d "{\"messages\":\"show space datas limit 5\",\"model\":\"gpt-4o\", \"chat_mode\": \"chat_data\", \"chat_param\": \"$DB_NAME\"}"
```

### 6.2 使用 Python 客户端
```python
from dbgpt_client import Client

DBGPT_API_KEY = "dbgpt"
DB_NAME = "{your_db_name}"

client = Client(api_key=DBGPT_API_KEY)
res = client.chat(
    messages="show space datas limit 5", 
    model="gpt-4o", 
    chat_mode="chat_data", 
    chat_param=DB_NAME
)
```

## 7. 知识库操作示例

### 7.1 创建知识空间
```python
from dbgpt_client import Client
from dbgpt_client.knowledge import create_space
from dbgpt_client.schema import SpaceModel

DBGPT_API_KEY = "dbgpt"
client = Client(api_key=DBGPT_API_KEY)

res = await create_space(
    client,
    SpaceModel(
        name="test_space",
        vector_type="Chroma",
        desc="for client space",
        owner="dbgpt",
    ),
)
```

### 7.2 创建文档
```python
from dbgpt_client.schema import DocumentModel

res = await create_document(
    client,
    DocumentModel(
        space_id="5",
        doc_name="test_doc",
        doc_type="TEXT",
        doc_content="test content",
        doc_source="",
    ),
)
```

## 8. 环境变量配置

### 8.1 基础配置
```bash
# API 密钥
API_KEYS=dbgpt

# DB-GPT API 基础地址
DBGPT_API_BASE=http://localhost:5670/api/v2

# DB-GPT API 密钥
DBGPT_API_KEY=dbgpt
```

### 8.2 代理模型配置
```bash
# OpenAI
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=https://api.openai.com/v1

# DeepSeek
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_API_BASE=https://api.deepseek.com/v1

# Moonshot
MOONSHOT_API_KEY=your_moonshot_api_key
MOONSHOT_API_BASE=https://api.moonshot.cn/v1
```

## 9. 错误处理

### 9.1 认证错误
```json
{
    "error": {
        "message": "",
        "type": "invalid_request_error",
        "param": null,
        "code": "invalid_api_key"
    }
}
```

### 9.2 通用响应格式
```json
{
    "success": true,
    "err_code": null,
    "err_msg": null,
    "data": {}
}
```

## 10. 注意事项

1. **API 版本兼容性**: 不同版本的 API 可能不完全兼容，请根据文档选择合适的版本
2. **知识库和提示 API**: 目前仍在测试阶段，将逐步开放
3. **超时设置**: 建议设置合适的超时时间，默认为 120 秒
4. **并发限制**: 请注意 API 调用频率限制

## 11. 更多资源

- **完整 API 文档**: `http://127.0.0.1:5670/docs`
- **GitHub 仓库**: https://github.com/eosphoros-ai/DB-GPT
- **官方文档**: https://docs.dbgpt.site/

---

*本文档基于 DB-GPT v0.7.2 版本编写，具体 API 接口以实际部署版本为准。*
