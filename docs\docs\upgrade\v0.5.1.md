# Upgrade To v0.5.1

## Overview

This guide is for upgrading from v0.5.0 to v0.5.1. If you use SQLite,
you not need to upgrade the database. If you use MySQL, you need to upgrade the database.

## Prepare

### Backup Your Database

To prevent data loss, it is recommended to back up your database before upgrading.
The backup way according to your database type.

## Upgrade

### Stop DB-GPT Service

Stop the DB-GPT service according to your start way.

### Upgrade Database

Execute the following SQL to upgrade the database.

**Add Columns**
```sql
USE dbgpt;
ALTER TABLE  dbgpt_serve_flow
    ADD COLUMN `error_message` varchar(512) null comment 'Error message' after `state`;
```
